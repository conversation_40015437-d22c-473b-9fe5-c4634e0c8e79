@Override
    protected void execute(EntityManager em, TransactionState<TransferRequest, TransferResponse> state) throws RuleCheckException {
        Session session = state.getSession();
        Transaction transaction = state.getTransaction();

        boolean haveToRefill = false;
        BigDecimal bBonusAmount = null;
        String refProfile = null;
        Account  aAccount = null;
        Account  bAccount = null;
        int currencyDecimalDigits = context.getMoneyScale();
        BigDecimal amount = BigDecimal.ZERO;

        BigDecimal buyerBalanceDelta = BigDecimal.ZERO;
        BigDecimal buyerTradeBonusProvisionDelta = BigDecimal.ZERO;
        BigDecimal buyerTradeBonusAmount = BigDecimal.ZERO;

        TransactionData dataMap = new TransactionData(); // dataMap for Rules Engine
        BigDecimal tradeBonusAdjustmentDelta = BigDecimal.ZERO;
        BigDecimal totalCampaignAccountDeductions = BigDecimal.ZERO;
        String tradeBonusAdjustmentAccountMsisdn = null;

        // Initialize with FAILED, will be changed to COMPLETED only if transaction is fully successful
        OutcomeNotificationRequest.Status outcomeStatus = OutcomeNotificationRequest.Status.FAILED;
        OutcomeNotificationRequest.ModificationStatus propertyModificationStatus = new OutcomeNotificationRequest.ModificationStatus();
        TransactionData outcomeAdditionalData = new TransactionData(); // Currently not populated, but available for future use

        amount = state.getRequest().getAmount();

        TransferRule transferRule = transaction.getTransferRule();

        BigDecimal buyerTradeBonusPercentage =
            (transferRule == null ? BigDecimal.ZERO : transferRule.getBuyerTradeBonusPercentage());

        // Initial calculation, may be overridden by Rules Engine
        buyerTradeBonusAmount = buyerTradeBonusPercentage.multiply(amount);

        IRulesEngineConnector rulesEngine = context.getRulesEngineConnector();
        boolean rulesEngineEnabled = (rulesEngine != null) && rulesEngine.isEnabled();
        boolean rulesEngineEvaluateCompleted = false;

        if (rulesEngineEnabled) {
            try {
                dataMap.put("sellerAgentId", state.getAgentA().getId());
                dataMap.put("sellerAgentMsisdn", state.getAgentA().getMobileNumber());
                dataMap.put("sellerAgentGroups", new ArrayList<>());
                dataMap.put("sellerAgentZones", new ArrayList<>());
                dataMap.put("sellerAgentCgi", Cell.toCgiString(transaction.getA_Cell()));
                dataMap.put("sellerAgentTierName", state.getAgentA().getTier().getName());
                dataMap.put("sellerAgentLanguage", state.getAgentA().getLanguage().toUpperCase());

                dataMap.put("buyerAgentId", state.getAgentB().getId());
                dataMap.put("buyerAgentMsisdn", state.getAgentB().getMobileNumber());
                dataMap.put("buyerAgentGroups", new ArrayList<>());
                dataMap.put("buyerAgentZones", new ArrayList<>());
                dataMap.put("buyerAgentCgi", Cell.toCgiString(transaction.getB_Cell()));
                dataMap.put("buyerAgentTierName", state.getAgentB().getTier().getName());
                dataMap.put("buyerAgentLanguage", state.getAgentB().getLanguage().toUpperCase());

                dataMap.put("purchaseAmount", amount.setScale(currencyDecimalDigits, RoundingMode.CEILING).doubleValue());
                dataMap.put("tradeBonusAmount", buyerTradeBonusAmount.setScale(currencyDecimalDigits, RoundingMode.CEILING).doubleValue());

                String channelName;
                switch (transaction.getChannel()) {
                    case Session.CHANNEL_USSD: channelName = "USSD"; break;
                    case Session.CHANNEL_SMS: channelName = "SMS"; break;
                    case Session.CHANNEL_3PP: channelName = "3PP"; break;
                    case Session.CHANNEL_SMART_DEVICE: channelName = "APP"; break;
                    case Session.CHANNEL_WUI: channelName = "GUI"; break;
                    case Session.CHANNEL_BATCH: channelName = "BATCH"; break;
                    default: channelName = "UNKNOWN"; break;
                }
                dataMap.put("transactionChannel", channelName);

                ObjectMapper objectMapper = new ObjectMapper();
                String evalRequestJson = objectMapper.writeValueAsString(dataMap);
                logger.info("RulesEngine pre-evaluate request: " + evalRequestJson);

                TransactionData evalData = new TransactionData(dataMap);
                EvaluateResponse evalResponse = rulesEngine.evaluate(
                    "WHOLESALE_AIRTIME_PURCHASE", transaction.getInboundTransactionID() + ".1", evalData);

                rulesEngineEvaluateCompleted = true; // Mark that evaluation attempt was made

                if (evalResponse != null) {
                    logger.info("RulesEngine pre-evaluate result: " + evalResponse.toString());

                    if (evalResponse.getModifiedProperties() != null) {
                        for (Map.Entry<String, EvaluateResponse.ModifiedProperty> entry : evalResponse.getModifiedProperties().entrySet()) {
                            String propertyName = entry.getKey();
                            EvaluateResponse.ModifiedProperty modifiedProperty = entry.getValue();

                            if ("tradeBonusAmount".equals(propertyName)) {
                                EvaluateResponse.VariantValue originalValue = modifiedProperty.getOriginal();
                                EvaluateResponse.VariantValue modifiedValue = modifiedProperty.getModified();

                                if (originalValue == null || modifiedValue == null) {
                                    logger.warn("RulesEngine evaluate: modified property [" + propertyName + "] has a null value, expected numeric, ignoring.");
                                    continue;
                                }

                                if (!originalValue.isNumeric() || !modifiedValue.isNumeric()) {
                                    logger.warn("RulesEngine evaluate: modified property [" + propertyName + "] has a non-numeric value, expected numeric, ignoring.");
                                    continue;
                                }

                                double original = originalValue.asDouble();
                                double modified = modifiedValue.asDouble();

                                logger.info("RulesEngine evaluate: modified property [" + propertyName + "] original value [" +original  + "] new value [" + modified + "].");

                                // Assuming "tradeBonusAmount" is the only modifiable property here as per original logic
                                logger.info("RulesEngine evaluate: trade bonus amount modified from [" + buyerTradeBonusAmount + "] to [" + modified  + "].");
                                // FIXME consider adding checks for modified > 0 and modified > original
                                buyerTradeBonusAmount = new BigDecimal(modified);
                                propertyModificationStatus.addApplied(propertyName);
                            }
                        }
                    } else {
                        logger.warn("RulesEngine evaluate: missing modifiedProperties in response, expected an array.");
                    }
                } else {
                    logger.warn("RulesEngine evaluate failed, ignoring the failure, proceeding with the transaction.");
                    // rulesEngineEvaluateCompleted is still true, indicating an attempt was made.
                    // The outcome notification later will reflect the transaction's success/failure with this knowledge.
                }
            } catch (Throwable ex) {
                logger.error("RulesEngine Exception (ignoring): " + ex.getMessage());
                // rulesEngineEvaluateCompleted remains false if exception before rulesEngine.evaluate call,
                // or true if exception was after a successful evaluate() call but during processing its response.
                // For simplicity, if we enter the try and fail, let's consider evaluate not fully completed for decision making,
                // but if `rulesEngine.evaluate` was called, `rulesEngineEvaluateCompleted` will be true.
                // The crucial part is `propertyModificationStatus` should be accurate.
            }
        }

        boolean overallSuccess = false; // Flag to track if all critical operations succeeded

        try { // Main transaction processing block
            try (RequiresTransaction trans = new RequiresTransaction(em)) {
                BigDecimal one = new BigDecimal(1.0);

                bAccount = findAccount(em, state.getAgentB().getId());
                state.setBeforeB(bAccount);

                BigDecimal cumulativeTradeBonusPercentage = state.getAgentB().getTier().getDownStreamPercentage();

                buyerBalanceDelta = amount.add(buyerTradeBonusAmount).setScale(currencyDecimalDigits, RoundingMode.CEILING);

                BigDecimal currentBuyerTradeBonusProvision = bAccount.getBonusBalance().setScale(currencyDecimalDigits, RoundingMode.CEILING);
                BigDecimal currentBuyerBalance = bAccount.getBalance();

                buyerTradeBonusProvisionDelta = currentBuyerBalance.add(buyerBalanceDelta)
                    .multiply(cumulativeTradeBonusPercentage)
                    .setScale(currencyDecimalDigits, RoundingMode.CEILING).subtract(currentBuyerTradeBonusProvision);

                transaction.setAmount(amount.setScale(currencyDecimalDigits, RoundingMode.CEILING));
                transaction.setGrossSalesAmount(amount.setScale(currencyDecimalDigits, RoundingMode.CEILING));
                transaction.setBuyerTradeBonusAmount(buyerTradeBonusAmount.setScale(currencyDecimalDigits, RoundingMode.CEILING));
                transaction.setBuyerTradeBonusProvision(buyerTradeBonusProvisionDelta.setScale(currencyDecimalDigits, RoundingMode.CEILING));
                transaction.setBuyerTradeBonusPercentage(buyerTradeBonusPercentage);

                bAccount.adjust(
                        buyerBalanceDelta,
                        buyerTradeBonusProvisionDelta,
                        false);

                aAccount = findAccount(em, state.getAgentA().getId());
                state.setBeforeA(aAccount);

                Transaction lastSuccessfulTxTransaction = Transaction.findLastSuccessfulTransferToAgent(em, transaction.getA_MSISDN(), transaction.getCompanyID());
                BigDecimal defTradeBonusPct = aAccount.getAgent().getTier().getBuyerDefaultTradeBonusPercentage();
                if((lastSuccessfulTxTransaction != null) && (lastSuccessfulTxTransaction.getBuyerTradeBonusPercentage() != null) && (lastSuccessfulTxTransaction.getAmount() != null)) {
                    MathContext mc = new MathContext(8, RoundingMode.HALF_UP);
                    BigDecimal lastCreditPurchased = lastSuccessfulTxTransaction.getAmount();
                    BigDecimal lastBonusAmount = lastSuccessfulTxTransaction.getBuyerTradeBonusAmount();
                    BigDecimal lastCreditReceived = lastCreditPurchased.add(lastBonusAmount);
                    BigDecimal costPerUnit = lastCreditPurchased.divide(lastCreditReceived, mc);
                    logger.info("Using bonus percentage {}% from the last transfer transaction id {}, cost per unit is {}.",
                        lastSuccessfulTxTransaction.getBuyerTradeBonusPercentage().multiply(new BigDecimal(100.0)).setScale(2, RoundingMode.HALF_UP), lastSuccessfulTxTransaction.getId(), costPerUnit);
                    transaction.setCostOfGoodsSold(amount.multiply(costPerUnit));
                } else if (defTradeBonusPct != null) {
                    MathContext mc = new MathContext(8, RoundingMode.HALF_UP);
                    logger.info("Using default bonus percentage {}% for calculating cost of goods sold", defTradeBonusPct.setScale(2, RoundingMode.HALF_UP));
                    transaction.setCostOfGoodsSold(amount.divide((defTradeBonusPct.add(new BigDecimal(100.0)).divide(new BigDecimal(100.0), mc)), mc));
                } else {
                    logger.warn("Bonus percentage unavailable for calculating cost of goods sold.");
                }

                boolean fromRoot = state.get(PROP_FROM_ROOT);
                aAccount.transact(
                        transaction.getStartTime(),
                        amount.setScale(currencyDecimalDigits, RoundingMode.CEILING),
                        BigDecimal.ZERO,
                        (buyerTradeBonusProvisionDelta.add(buyerTradeBonusAmount)).negate() // This seems to be an error from original, should be .negate() once for cost
                        .setScale(currencyDecimalDigits, RoundingMode.CEILING), // Check: original had .negate().negate()
                        fromRoot);
                // Corrected based on common patterns, assuming it's a cost to A:
                // (buyerTradeBonusProvisionDelta.add(buyerTradeBonusAmount)).negate().setScale(currencyDecimalDigits, RoundingMode.CEILING),

                transaction.testAmlLimitsA(aAccount, amount.setScale(currencyDecimalDigits, RoundingMode.CEILING));

                haveToRefill = transferRule != null && transferRule.getTargetBonusPercentage() != null;

                if (haveToRefill) {
                    bBonusAmount = amount.multiply(transferRule.getTargetBonusPercentage()).setScale(0, RoundingMode.UP);
                    refProfile = transferRule.getTargetBonusProfile();
                    transaction.setB_TransferBonusAmount(bBonusAmount.setScale(currencyDecimalDigits, RoundingMode.CEILING));
                    transaction.setB_TransferBonusProfile(refProfile);
                    transaction.setFollowUp(true); // Set follow-up before AIR call
                }

                setTransactionAAfter(transaction, aAccount);
                setTransactionBAfter(transaction, bAccount);

                transaction.persist(em, null, state.getSession(), null);

                Double longitude = state.getRequest().getLongitude();
                Double latitude = state.getRequest().getLatitude();
                long transactionId = transaction.getId();

                if (longitude != null && latitude != null) {
                    transaction.persistTransactionLocation(em, longitude, latitude, transactionId);
                }

                updateInDb(em, trans, transaction, aAccount, bAccount);
                totalCampaignAccountDeductions = tradeBonusAdjustmentDelta; // This variable is not modified anywhere else in the snippet.
            } // trans.commit() happens here if no exceptions

            // If we reach here, database operations were successful.
            // Now, handle AIR call if needed.
            if (!haveToRefill) {
                overallSuccess = true; // DB success and no AIR call needed
            } else {
                ITransaction tx = defineAirTransaction(transaction);
                Subscriber subscriber = new Subscriber(transaction.getB_MSISDN(), context.getAirConnector(), tx);
                TransfersConfig config = state.getConfig(em, TransfersConfig.class); // em is closed by this point if used as argument.
                                                                                      // Original code used em.close() then new EntityManagerEx for AIR.
                                                                                      // Let's assume config is fetched before em is closed or is not dependent on this em.
                                                                                      // For safety, fetch config before em.close() if it uses em.
                ReversalsConfig reversalsConfig = state.getConfig(em, ReversalsConfig.class);


                em.close(); // Close the initial EntityManager as per original flow before AIR call

                try { // AIR call for refill
                    IAirConnector air = context.getAirConnector();
                    long longAmount = air.toLongAmount(bBonusAmount);
                    List<String> externalDataList =
                        this.expandExternalDataList(
                                Arrays.asList(
                                    config.getRefillExternalData1(),
                                    config.getRefillExternalData2(),
                                    config.getRefillExternalData3(),
                                    config.getRefillExternalData4()),
                                config.listExternalDataFields(), state);

                    RefillResponse result = subscriber.refillAccount(refProfile, longAmount, false,
                            externalDataList, reversalsConfig.isEnableDedicatedAccountReversal());

                    DedicatedAccountRefillInfoAccounts dARefillInfoList = createDaRefillInfoList(result);

                    try (EntityManagerEx em2 = context.getEntityManager()) {
                        try (RequiresTransaction trans = new RequiresTransaction(em2)) {
                            // Re-fetch entities with the new EntityManager
                            aAccount = findAccount(em2, aAccount.getID());
                            bAccount = findAccount(em2, bAccount.getID());
                            transaction = Transaction.findByID(em2, transaction.getId(), transaction.getCompanyID());
                            state.setTransaction(transaction); // Update state with re-fetched transaction

                            if (dARefillInfoList != null && !isEmpty(dARefillInfoList.getDedicatedAccountRefillInfos())) {
                                logger.debug(" size dARefillInfoList to save: {}", dARefillInfoList.getDedicatedAccountRefillInfos().size());
                                transaction.addExtraDataForKeyType(TransactionExtraData.Key.DEDICATED_ACCOUNT_REFILL_INFO, dARefillInfoList);
                            }
                            transaction.persistExtraData(em2);

                            state.set(PROP_SUBSCRIBER_LANGUAGE, subscriber.getLanguageCode2());
                            transaction.setFollowUp(false); // Clear follow-up on successful refill
                            setTransactionAAfter(transaction, aAccount);
                            setTransactionBAfter(transaction, bAccount);
                            updateInDb(em2, trans, transaction, aAccount, bAccount);
                            overallSuccess = true; // AIR call and subsequent updates were successful
                        }
                    }
                } catch (AirException e) {
                    logger.warn("Air Exception", e);
                    overallSuccess = false; // AIR call failed
                    // Original AirException handling logic:
                    try (EntityManagerEx em2 = context.getEntityManager()) {
                        try (RequiresTransaction trans = new RequiresTransaction(em2)) {
                            aAccount = findAccount(em2, aAccount.getID());
                            bAccount = findAccount(em2, bAccount.getID());
                            transaction = Transaction.findByID(em2, transaction.getId(), transaction.getCompanyID());
                            state.setTransaction(transaction);

                            if (e.isDeterministic() && isDeterministic(config.getNonDeterministicErrorCodes(), e.getResponseCode())) { // config might be stale if em was closed.
                                state.exitWith(mapAirResponseCode(e.getResponseCode()), e.getMessage());
                                transaction.setLastExternalResultCode(Integer.toString(e.getResponseCode()));
                                transaction.setBuyerTradeBonusProvision(BigDecimal.ZERO); // Reversal logic
                                transaction.setFollowUp(false);

                                // Adjust accounts back
                                aAccount.adjust(
                                        amount.setScale(currencyDecimalDigits, RoundingMode.CEILING),
                                        (buyerTradeBonusProvisionDelta.add(buyerTradeBonusAmount)).negate()
                                        .setScale(currencyDecimalDigits, RoundingMode.CEILING), false);
                                bAccount.adjust(
                                        buyerBalanceDelta.negate().setScale(currencyDecimalDigits, RoundingMode.CEILING),
                                        buyerTradeBonusProvisionDelta.negate().setScale(currencyDecimalDigits, RoundingMode.CEILING),
                                        false);
                            } else {
                                state.getResponse().setAdditionalInformation(String.format("Transfer bonus credit may have failed - Follow Up! Air-Node: [%s]", e.getHost()));
                                state.getResponse().setFollowUp(true);
                                // transaction.setFollowUp(true) was already set before AIR call
                            }
                            setTransactionAAfter(transaction, aAccount);
                            setTransactionBAfter(transaction, bAccount); // bAccount state after adjustments
                            // The original code missed bAccount in updateInDb during AirException: updateInDb(em2, trans, transaction, aAccount);
                            // It should likely be:
                            updateInDb(em2, trans, transaction, aAccount, bAccount);
                        }
                    }
                }
            }
        } catch (RuleCheckException rce) {
            overallSuccess = false; // Mark failure
            throw rce; // Re-throw to be handled by the caller as per original contract
        } catch (Throwable unhandledException) {
            // Catch any other unexpected exception during the main processing
            logger.error("Unexpected exception during transaction processing (after Rules Engine if enabled): " + unhandledException.getMessage(), unhandledException);
            overallSuccess = false; // Mark failure
            // Depending on policy, you might want to wrap and rethrow as RuleCheckException or a runtime one
            // For minimal change, if this would have propagated before, let it.
            // However, to ensure outcome notification is sent, it's better to handle it here if possible
            // or ensure the method contract allows such throwables.
            // If it's a RuntimeException, it will propagate and might bypass the notification.
            // If it's a checked Exception not declared, it's a compile error.
            // For now, we assume RuleCheckException is the main declared checked exception.
            if (unhandledException instanceof RuntimeException) {
                 throw (RuntimeException) unhandledException;
            }
            // Or wrap in a generic RuleCheckException if appropriate for the framework
            // throw new RuleCheckException(StatusCode.INTERNAL_SERVER_ERROR, "unexpected", "Unexpected error during transaction processing.");
        }


        // Send Outcome Notification to Rules Engine (moved to the end)
        if (rulesEngineEvaluateCompleted) {
            if (overallSuccess) {
                outcomeStatus = OutcomeNotificationRequest.Status.COMPLETED;
            } else {
                // outcomeStatus remains FAILED (its initial value)
                // Check if any modifications were applied and should be marked as rejected if transaction failed overall
                // The current propertyModificationStatus only contains 'applied' modifications from CRE's perspective.
                // If the transaction failed *after* CRE applied them, they were 'applied' by CRE but the transaction outcome is FAILED.
                // This seems to be the correct interpretation for the notification.
            }

            try {
                // Ensure dataMap contains the final state if any crucial data changed after initial population.
                // However, dataMap is generally for the state *at the time of evaluation*.
                // Re-populating or updating dataMap here could be complex.
                // Using the dataMap as it was when evaluate was called is standard.
                TransactionData notificationTxData = new TransactionData(dataMap);

                // Add any additional outcome-specific data if available
                // outcomeAdditionalData.put("finalTransactionStatus", transaction.getReturnCode()); // Example

                rulesEngine.outcomeNotification(
                    "WHOLESALE_AIRTIME_PURCHASE",
                    transaction.getInboundTransactionID(), // Use the final transaction object's ID
                    outcomeStatus,
                    notificationTxData,       // Data as it was sent to rules engine for evaluation
                    propertyModificationStatus, // Modifications applied by rules engine
                    outcomeAdditionalData);     // Any extra data about the outcome
            } catch (Throwable ex) {
                logger.error("RulesEngine Exception (outcome notification) (ignoring): " + ex.getMessage());
            }
        }
        // The original `if (!haveToRefill) { return; }` is removed as logic now proceeds to send notification.
        // The method is void, so it will return naturally after this.
    }